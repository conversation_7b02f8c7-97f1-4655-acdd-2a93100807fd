#!/usr/bin/env python3
"""
Debug workflow local reading
"""
import sys
import json
from pathlib import Path

# Add the app directory to the path
sys.path.insert(0, str(Path(__file__).parent))

from app.services.workflow_service import workflow_service

def debug_workflow_local():
    """Debug workflow local reading step by step"""
    print("🔍 Debugging Workflow Local Reading")
    print("=" * 50)
    
    key = "RDY_pattern-extraction"
    
    # Step 1: Check paths
    wf_dir = workflow_service._workflow_dir_for_key(key)
    metadata_path = wf_dir / "metadata.json"
    workflow_path = wf_dir / "workflow.json"
    
    print(f"📁 Workflow dir: {wf_dir}")
    print(f"📄 Metadata path: {metadata_path}")
    print(f"📄 Workflow path: {workflow_path}")
    
    print(f"✅ Metadata exists: {metadata_path.exists()}")
    print(f"✅ Workflow exists: {workflow_path.exists()}")
    
    if not (metadata_path.exists() and workflow_path.exists()):
        print("❌ Files don't exist, returning None")
        return
    
    # Step 2: Read metadata
    try:
        print("\n📖 Reading metadata...")
        with open(metadata_path, "r", encoding="utf-8") as f:
            metadata_data = json.load(f)
        print(f"✅ Metadata loaded: {metadata_data.get('name', 'Unknown')}")
    except Exception as e:
        print(f"❌ Metadata read failed: {e}")
        return
    
    # Step 3: Read workflow with detailed debugging
    print("\n📖 Reading workflow...")
    try:
        print(f"📊 File size: {workflow_path.stat().st_size} bytes")
        
        with open(workflow_path, "r", encoding="utf-8") as f:
            content = f.read()
            print(f"📊 Content length: {len(content)} chars")
            print(f"📊 Content preview: {repr(content[:100])}")
            
            content_stripped = content.strip()
            print(f"📊 Stripped length: {len(content_stripped)} chars")
            
            if not content_stripped:
                print("❌ Content is empty after stripping")
                return
            
            print("🔄 Parsing JSON...")
            workflow_json = json.loads(content_stripped)
            print(f"✅ JSON parsed successfully, keys: {len(workflow_json)}")
            
            if not isinstance(workflow_json, dict) or not workflow_json:
                print("❌ JSON is not a valid dict or is empty")
                return
            
            print("✅ Workflow JSON is valid")
            
    except Exception as e:
        print(f"❌ Workflow read failed: {e}")
        import traceback
        traceback.print_exc()
        return
    
    print("\n✅ All checks passed - workflow should load successfully")

if __name__ == "__main__":
    debug_workflow_local()
