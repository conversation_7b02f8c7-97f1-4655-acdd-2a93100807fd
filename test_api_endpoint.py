#!/usr/bin/env python3
"""
Test API endpoint for workflow retrieval
"""
import sys
import json
import httpx
from pathlib import Path

# Add the app directory to the path
sys.path.insert(0, str(Path(__file__).parent))

def test_api_endpoint():
    """Test the workflow API endpoint"""
    print("🧪 Testing Workflow API Endpoint")
    print("=" * 50)
    
    workflow_key = "rdy_pattern-extraction"
    base_url = "http://localhost:8001"  # 假设后端在8001端口
    
    try:
        # Test the API endpoint
        url = f"{base_url}/workflows/{workflow_key}"
        print(f"📡 Testing URL: {url}")
        
        with httpx.Client(timeout=10.0) as client:
            response = client.get(url)
            
        print(f"📊 Status Code: {response.status_code}")
        print(f"📊 Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print(f"✅ JSON Response received")
                print(f"📊 Success: {data.get('success', 'N/A')}")
                print(f"📊 Message: {data.get('message', 'N/A')}")
                
                workflow_data = data.get('data', {})
                if workflow_data:
                    print(f"📊 Workflow name: {workflow_data.get('metadata', {}).get('name', 'N/A')}")
                    workflow_json = workflow_data.get('workflow_json', {})
                    print(f"📊 Node count: {len(workflow_json) if workflow_json else 0}")
                else:
                    print("❌ No workflow data in response")
                    
            except json.JSONDecodeError as e:
                print(f"❌ Invalid JSON response: {e}")
                print(f"📄 Response text: {response.text[:200]}...")
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            print(f"📄 Response: {response.text[:200]}...")
            
    except httpx.ConnectError:
        print(f"❌ Connection failed - backend not running on {base_url}")
    except Exception as e:
        print(f"❌ Request failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_api_endpoint()
