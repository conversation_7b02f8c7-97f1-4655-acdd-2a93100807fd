import streamlit as st
import httpx
import asyncio
from typing import Dict, Any, Optional, List
import os
from datetime import datetime
import json
import subprocess
import tkinter as tk
from tkinter import filedialog
import threading
from typing import Dict, Any, Optional, List
import sys
from pathlib import Path
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 确保可以以包形式导入 app.*（Streamlit 运行时常见问题）
# 将仓库根目录加入 sys.path（.../FreemanWorkHub）
_ROOT = Path(__file__).resolve().parents[2]
if _ROOT.name != 'FreemanWorkHub':
    _ROOT = Path(__file__).resolve().parents[1]
if str(_ROOT) not in sys.path:
    sys.path.insert(0, str(_ROOT))

# 统一使用 workflow_utils 的解析逻辑，避免重复与不一致
from app.ui.workflows.workflow_utils import (
    extract_notes_from_workflow as wu_extract_notes,
    classify_workflow_type as wu_classify_type,
    analyze_workflow_nodes as wu_analyze_nodes,
)

# 工作流配置页面功能将在主文件中实现

# 页面配置
st.set_page_config(
    page_title="FreemanWorkHub",
    page_icon="🎨",
    layout="wide",
    initial_sidebar_state="expanded"
)

# API 基础地址
API_BASE_URL = f"http://localhost:{os.getenv('API_PORT', '18001')}"

# 同步请求辅助函数
def sync_api_request(method: str, endpoint: str, headers: Optional[Dict] = None, **kwargs) -> Dict[str, Any]:
    """发送同步 API 请求"""
    import requests
    
    url = f"{API_BASE_URL.rstrip('/')}/{endpoint.lstrip('/')}"
    
    try:
        response = requests.request(method, url, headers=headers, timeout=10, **kwargs)
        
        if response.status_code == 200:
            return response.json()
        elif response.status_code == 401:
            # 认证失败，清除登录状态
            if "logged_in" in st.session_state:
                del st.session_state.logged_in
                del st.session_state.token
                del st.session_state.user_info
            st.error("登录已过期，请重新登录")
            st.stop()
        else:
            error_detail = "未知错误"
            try:
                error_detail = response.json().get("detail", f"HTTP {response.status_code}")
            except:
                error_detail = f"HTTP {response.status_code}"
            st.error(f"API 请求失败: {error_detail}")
            return {"success": False, "error": error_detail}
    except Exception as e:
        st.error(f"网络错误: {str(e)}")
        return {"success": False, "error": str(e)}

# 异步请求辅助函数 (保留以兼容旧代码)
async def api_request(method: str, endpoint: str, headers: Optional[Dict] = None, **kwargs) -> Dict[str, Any]:
    """发送 API 请求"""
    url = f"{API_BASE_URL.rstrip('/')}/{endpoint.lstrip('/')}"
    
    async with httpx.AsyncClient() as client:
        try:
            response = await client.request(method, url, headers=headers, **kwargs)
            response.raise_for_status()
            return response.json()
        except httpx.HTTPStatusError as e:
            if e.response.status_code == 401:
                # 认证失败，清除登录状态
                if "logged_in" in st.session_state:
                    del st.session_state.logged_in
                    del st.session_state.token
                    del st.session_state.user_info
                st.error("登录已过期，请重新登录")
                st.stop()
            else:
                st.error(f"API 请求失败: {e.response.status_code} - {e.response.text}")
                return {"success": False, "error": e.response.text}
        except Exception as e:
            st.error(f"网络错误: {str(e)}")
            return {"success": False, "error": str(e)}

def get_auth_headers() -> Dict[str, str]:
    """获取认证头"""
    if "token" in st.session_state:
        return {"Authorization": f"Bearer {st.session_state.token}"}
    return {}

def select_folder_with_dialog():
    """使用系统文件对话框选择文件夹"""
    try:
        # 创建隐藏的根窗口
        root = tk.Tk()
        root.withdraw()  # 隐藏主窗口
        root.attributes('-topmost', True)  # 置顶显示
        
        # 打开文件夹选择对话框
        folder_path = filedialog.askdirectory(
            title="选择 ComfyUI 工作流目录",
            initialdir=os.path.expanduser("~")
        )
        
        # 清理tkinter资源
        root.destroy()
        
        return folder_path if folder_path else None
        
    except Exception as e:
        print(f"文件夹选择失败: {e}")
        return None

def show_login_page():
    """显示登录页面"""
    st.title("🔐 登录 FreemanWorkHub")
    
    with st.form("login_form"):
        username = st.text_input("用户名")
        access_key = st.text_input("访问密钥", type="password")
        
        if st.form_submit_button("登录", type="primary", use_container_width=True):
            if username and access_key:
                with st.spinner("正在验证..."):
                    login_data = {"username": username, "access_key": access_key}
                    
                    try:
                        import requests
                        response = requests.post(
                            f"{API_BASE_URL}/auth/login",
                            json=login_data,
                            timeout=10
                        )
                        
                        if response.status_code == 200:
                            result = response.json()
                            # 登录成功
                            st.session_state.logged_in = True
                            st.session_state.token = result["token"]
                            st.session_state.user_info = result["user"]
                            st.success("登录成功！")
                            st.rerun()
                        else:
                            # 安全地解析错误响应
                            try:
                                error_data = response.json()
                                error_detail = error_data.get("detail", "登录失败")
                            except ValueError:
                                # 如果响应不是JSON格式，使用状态码和原始文本
                                error_detail = f"HTTP {response.status_code}: {response.text[:100]}"
                            st.error(f"登录失败: {error_detail}")
                    except Exception as e:
                        st.error(f"登录请求失败: {str(e)}")
            else:
                st.error("请输入用户名和访问密钥")

def show_home_page():
    """显示主页"""
    st.title("🏠 FreemanWorkHub")
    
    # 检查登录状态
    if "logged_in" not in st.session_state or not st.session_state.logged_in:
        show_login_page()
        return
    
    # 显示用户信息
    col1, col2 = st.columns([3, 1])
    with col1:
        username = st.session_state.user_info.get("username", "用户")
        st.subheader(f"欢迎回来，{username}!")
    with col2:
        if st.button("🚪 退出登录"):
            del st.session_state.logged_in
            del st.session_state.token
            del st.session_state.user_info
            st.rerun()
    
    # 检查API连接状态
    try:
        health = sync_api_request("GET", "/health")
        
        if health.get("success") != False:
            st.success("✅ API 服务连接正常")
        else:
            st.error("❌ API 服务连接异常")
    except:
        st.warning("⚠️ 无法连接到API服务，请检查后端是否运行")
    
    # 工作流Gallery
    st.markdown("---")
    st.subheader("🎨 工作流 Gallery")
    
    # 获取按目录分组的工作流列表
    try:
        grouped_workflows_result = sync_api_request("GET", "/workflows/grouped", headers=get_auth_headers())
        
        # 检查是否是错误响应
        if isinstance(grouped_workflows_result, dict) and grouped_workflows_result.get("success") == False:
            st.error("获取工作流列表失败")
        elif isinstance(grouped_workflows_result, dict):
            # 后端返回按目录分组的工作流字典
            grouped_workflows = grouped_workflows_result
            
            if grouped_workflows:
                # 为每个目录创建展开器或直接显示
                for directory, workflows in grouped_workflows.items():
                    if directory == "根目录" or directory == "":
                        # 根目录的工作流直接显示
                        st.markdown("### 📂 根目录工作流")
                        display_workflows_in_grid(workflows)
                    else:
                        # 子目录的工作流用展开器显示，显示完整路径
                        dir_display_name = directory if directory else "未分类"
                        with st.expander(f"📁 {dir_display_name} ({len(workflows)} 个工作流)", expanded=True):
                            display_workflows_in_grid(workflows)
                            
            else:
                st.info("📂 暂无工作流，请先配置工作流目录")
                if st.button("⚙️ 前往工作流管理", use_container_width=True):
                    st.session_state.page = "workflows"
                    st.rerun()
        else:
            st.error("获取工作流列表失败")
    except Exception as e:
        st.error(f"获取工作流列表时出错: {str(e)}")

def display_workflows_in_management_grid(workflows, show_edit_button=False, key_prefix=""):
    """在工作流管理页面中以网格形式显示工作流列表"""
    if not workflows:
        return
    
    # 添加响应式网格CSS样式（与主页相同的样式）
    st.markdown("""
    <style>
    .workflow-grid-container {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: 1rem;
        margin: 1rem 0;
    }
    .workflow-card {
        border: 1px solid #e0e0e0;
        border-radius: 0.5rem;
        padding: 1rem;
        background: white;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        transition: box-shadow 0.3s ease;
        height: fit-content;
    }
    .workflow-card:hover {
        box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    }
    .workflow-title {
        font-weight: bold;
        font-size: 1.1rem;
        margin-bottom: 0.5rem;
        color: #333;
    }
    .workflow-description {
        color: #666;
        font-size: 0.9rem;
        margin-bottom: 0.5rem;
        line-height: 1.4;
    }
    .workflow-file {
        color: #888;
        font-size: 0.8rem;
        margin-bottom: 0.5rem;
    }
    .workflow-path {
        color: #888;
        font-size: 0.8rem;
        margin-bottom: 0.5rem;
    }
    .workflow-tags {
        color: #2e8b57;
        font-size: 0.8rem;
        font-weight: bold;
        margin-bottom: 1rem;
    }
    .workflow-status {
        color: #f39c12;
        font-weight: bold; 
        font-size: 0.9rem;
        margin-bottom: 1rem;
    }
    /* 深色主题支持 */
    .stApp[data-testid="stApp"] .workflow-card {
        background: #1e1e1e !important;
        border-color: #333 !important;
        color: #fff !important;
    }
    .stApp[data-testid="stApp"] .workflow-title {
        color: #fff !important;
    }
    .stApp[data-testid="stApp"] .workflow-description {
        color: #ccc !important;
    }
    .stApp[data-testid="stApp"] .workflow-file {
        color: #aaa !important;
    }
    .stApp[data-testid="stApp"] .workflow-path {
        color: #aaa !important;
    }
    .stApp[data-testid="stApp"] .workflow-tags {
        color: #90ee90 !important;
    }
    </style>
    """, unsafe_allow_html=True)
    
    # 计算每行的列数（响应式）
    num_workflows = len(workflows)
    if num_workflows <= 2:
        cols_per_row = num_workflows
    elif num_workflows <= 4:
        cols_per_row = 2
    else:
        cols_per_row = 3
    
    # 分行显示工作流
    rows = (num_workflows + cols_per_row - 1) // cols_per_row
    for row in range(rows):
        cols = st.columns(cols_per_row)
        for col_idx in range(cols_per_row):
            workflow_idx = row * cols_per_row + col_idx
            if workflow_idx < num_workflows:
                workflow = workflows[workflow_idx]
                with cols[col_idx]:
                    # 使用Streamlit原生容器创建卡片效果
                    with st.container():
                        # 获取工作流信息
                        title = workflow.get('metadata', {}).get('name', '未知工作流')
                        description = workflow.get("metadata", {}).get("description", "")
                        tags = workflow.get("metadata", {}).get("tags", [])
                        tags_str = ", ".join(tags) if tags else "无标签"
                        
                        # 获取文件路径信息
                        file_info = ""
                        if hasattr(workflow, 'file_path') or 'file_path' in workflow:
                            file_path = getattr(workflow, 'file_path', workflow.get('file_path', ''))
                            if file_path:
                                file_info = f"📄 {os.path.basename(file_path)}"
                        
                        # 获取相对路径信息
                        path_info = ""
                        if hasattr(workflow, 'relative_path') or 'relative_path' in workflow:
                            relative_path = getattr(workflow, 'relative_path', workflow.get('relative_path', ''))
                            if relative_path:
                                path_info = f"📂 {relative_path}"
                        
                        # 获取目录信息
                        parent_dir = getattr(workflow, 'parent_dir', '') or workflow.get('parent_dir', '') or '根目录'
                        
                        # 判断是否为DEV工作流
                        is_dev = "DEV" in tags
                        
                        # 创建自定义HTML卡片
                        card_html = f"""
                        <div class="workflow-card">
                            <div class="workflow-title">{title}</div>
                            {f'<div class="workflow-description">{description}</div>' if description else ''}
                            <div class="workflow-tags">🏷️ {tags_str}</div>
                            {f'<div class="workflow-file">{file_info}</div>' if file_info else ''}
                            {f'<div class="workflow-path">{path_info}</div>' if path_info else ''}
                            <div class="workflow-path">📁 目录: {parent_dir}</div>
                            {f'<div class="workflow-status">🔧 开发中 - 仅供查看</div>' if is_dev else ''}
                        </div>
                        """
                        st.markdown(card_html, unsafe_allow_html=True)
                        
                        # 按钮区域 - 统一使用"转到工作流"按钮
                        if st.button("🎯 转到工作流", key=f"{key_prefix}_goto_{workflow['key']}", use_container_width=True):
                            st.session_state.selected_workflow = workflow
                            st.session_state.page = "workflow_config"
                            st.rerun()

def display_workflows_in_grid(workflows):
    """在网格中显示工作流列表"""
    # 按标签分组
    dev_workflows = [w for w in workflows if w.get("metadata", {}).get("tags", []) and "DEV" in w["metadata"]["tags"]]
    rdy_workflows = [w for w in workflows if w.get("metadata", {}).get("tags", []) and "RDY" in w["metadata"]["tags"]]
    
    # 添加响应式网格CSS样式
    st.markdown("""
    <style>
    .workflow-grid-container {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: 1rem;
        margin: 1rem 0;
    }
    .workflow-card {
        border: 1px solid #e0e0e0;
        border-radius: 0.5rem;
        padding: 1rem;
        background: white;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        transition: box-shadow 0.3s ease;
        height: fit-content;
    }
    .workflow-card:hover {
        box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    }
    .workflow-title {
        font-weight: bold;
        font-size: 1.1rem;
        margin-bottom: 0.5rem;
        color: #333;
    }
    .workflow-description {
        color: #666;
        font-size: 0.9rem;
        margin-bottom: 0.5rem;
        line-height: 1.4;
    }
    .workflow-file {
        color: #888;
        font-size: 0.8rem;
        margin-bottom: 1rem;
    }
    .workflow-status {
        color: #f39c12;
        font-weight: bold; 
        font-size: 0.9rem;
        margin-bottom: 1rem;
    }
    /* 深色主题支持 */
    .stApp[data-testid="stApp"] .workflow-card {
        background: #1e1e1e !important;
        border-color: #333 !important;
        color: #fff !important;
    }
    .stApp[data-testid="stApp"] .workflow-title {
        color: #fff !important;
    }
    .stApp[data-testid="stApp"] .workflow-description {
        color: #ccc !important;
    }
    .stApp[data-testid="stApp"] .workflow-file {
        color: #aaa !important;
    }
    </style>
    """, unsafe_allow_html=True)
    
    # RDY工作流（可用）
    if rdy_workflows:
        st.markdown("#### ✅ 可用工作流")
        
        # 计算每行的列数（响应式）
        num_workflows = len(rdy_workflows)
        if num_workflows <= 2:
            cols_per_row = num_workflows
        elif num_workflows <= 4:
            cols_per_row = 2
        else:
            cols_per_row = 3
        
        # 分行显示工作流
        rows = (num_workflows + cols_per_row - 1) // cols_per_row
        for row in range(rows):
            cols = st.columns(cols_per_row)
            for col_idx in range(cols_per_row):
                workflow_idx = row * cols_per_row + col_idx
                if workflow_idx < num_workflows:
                    workflow = rdy_workflows[workflow_idx]
                    with cols[col_idx]:
                        # 使用Streamlit原生容器创建卡片效果
                        with st.container():
                            # 自定义HTML卡片
                            title = workflow['metadata']['name']
                            description = workflow.get("metadata", {}).get("description", "")
                            
                            # 获取文件路径信息
                            file_info = ""
                            if hasattr(workflow, 'file_path') or 'file_path' in workflow:
                                file_path = getattr(workflow, 'file_path', workflow.get('file_path', ''))
                                if file_path:
                                    file_info = f"📄 {os.path.basename(file_path)}"
                            
                            card_html = f"""
                            <div class="workflow-card">
                                <div class="workflow-title">{title}</div>
                                {f'<div class="workflow-description">{description}</div>' if description else ''}
                                {f'<div class="workflow-file">{file_info}</div>' if file_info else ''}
                            </div>
                            """
                            st.markdown(card_html, unsafe_allow_html=True)
                            
                            # 转到工作流按钮
                            if st.button("🎯 转到工作流", key=f"goto_{workflow['key']}", use_container_width=True):
                                st.session_state.selected_workflow = workflow
                                st.session_state.page = "workflow_config"
                                st.rerun()
    
    # DEV工作流（开发中）
    if dev_workflows:
        st.markdown("#### 🔧 开发中工作流")
        
        # 计算每行的列数（响应式）
        num_workflows = len(dev_workflows)
        if num_workflows <= 2:
            cols_per_row = num_workflows
        elif num_workflows <= 4:
            cols_per_row = 2
        else:
            cols_per_row = 3
        
        # 分行显示工作流
        rows = (num_workflows + cols_per_row - 1) // cols_per_row
        for row in range(rows):
            cols = st.columns(cols_per_row)
            for col_idx in range(cols_per_row):
                workflow_idx = row * cols_per_row + col_idx
                if workflow_idx < num_workflows:
                    workflow = dev_workflows[workflow_idx]
                    with cols[col_idx]:
                        # 使用Streamlit原生容器创建卡片效果
                        with st.container():
                            title = workflow['metadata']['name']
                            description = workflow.get("metadata", {}).get("description", "")
                            
                            # 获取文件路径信息
                            file_info = ""
                            if hasattr(workflow, 'file_path') or 'file_path' in workflow:
                                file_path = getattr(workflow, 'file_path', workflow.get('file_path', ''))
                                if file_path:
                                    file_info = f"📄 {os.path.basename(file_path)}"
                            
                            card_html = f"""
                            <div class="workflow-card">
                                <div class="workflow-title">{title}</div>
                                {f'<div class="workflow-description">{description}</div>' if description else ''}
                                {f'<div class="workflow-file">{file_info}</div>' if file_info else ''}
                                <div class="workflow-status">🔧 开发中 - 仅供查看</div>
                            </div>
                            """
                            st.markdown(card_html, unsafe_allow_html=True)

def show_workflows_page():
    """显示工作流管理页面"""
    st.title("⚙️ 工作流管理")
    
    # 检查登录状态
    if "logged_in" not in st.session_state or not st.session_state.logged_in:
        st.warning("⚠️ 请先登录才能使用工作流管理功能")
        show_login_page()
        return
    
    # 检查是否已设置ComfyUI工作流目录
    if "comfyui_workflow_dir" not in st.session_state:
        st.session_state.comfyui_workflow_dir = ""
    
    # 目录设置区域
    with st.expander("📁 ComfyUI 工作流目录设置", expanded=(st.session_state.comfyui_workflow_dir == "")):
        # 显示当前设置的目录
        if st.session_state.comfyui_workflow_dir:
            st.success(f"✅ 当前工作流目录: `{st.session_state.comfyui_workflow_dir}`")
            
            # 提供一些快捷操作
            col1, col2, col3 = st.columns(3)
            with col1:
                if st.button("🔄 重新选择目录", use_container_width=True):
                    st.session_state.comfyui_workflow_dir = ""
                    st.rerun()
            with col2:
                if st.button("📂 打开目录", use_container_width=True):
                    try:
                        if os.name == "nt":  # Windows
                            os.startfile(st.session_state.comfyui_workflow_dir)
                        else:  # Linux/Mac
                            subprocess.run(["xdg-open", st.session_state.comfyui_workflow_dir])
                        st.info("已在文件管理器中打开目录")
                    except:
                        st.warning("无法打开目录，请手动导航")
            with col3:
                if st.button("🔍 查看文件", use_container_width=True):
                    try:
                        files = os.listdir(st.session_state.comfyui_workflow_dir)
                        json_files = [f for f in files if f.endswith(".json")]
                        file_list = "', '".join(json_files[:5])
                        if len(json_files) > 5:
                            file_list += "..."
                        st.info(f"目录中找到 {len(json_files)} 个JSON文件: {file_list}")
                    except:
                        st.error("无法读取目录内容")
        else:
            st.info("请点击下方按钮打开文件夹选择对话框")
            
            # 文件夹选择按钮
            col1, col2 = st.columns(2)
            with col1:
                if st.button("📁 浏览并选择文件夹", type="primary", use_container_width=True):
                    # 使用文件对话框选择文件夹
                    selected_folder = select_folder_with_dialog()
                    if selected_folder:
                        st.session_state.comfyui_workflow_dir = selected_folder
                        st.success(f"已选择目录: `{selected_folder}`")
                        st.rerun()
                    else:
                        st.info("未选择目录")
            
            with col2:
                if st.button("🔍 自动检测", use_container_width=True):
                    # 自动检测常见的ComfyUI路径
                    detected_paths = []
                    search_paths = [
                        "~/Documents/ComfyUI/user/default/workflows",
                        "~/Documents/ComfyUI/user/workflows", 
                        "~/ComfyUI/workflows",
                        "~/AI/ComfyUI/workflows"
                    ]
                    
                    for path_template in search_paths:
                        actual_path = os.path.expanduser(path_template)
                        if os.path.exists(actual_path):
                            detected_paths.append(actual_path)
                    
                    if detected_paths:
                        st.success(f"检测到 {len(detected_paths)} 个可能的工作流目录")
                        for path in detected_paths:
                            if st.button(f"选择: {path}", key=f"detected_{path}"):
                                st.session_state.comfyui_workflow_dir = path
                                st.success(f"已选择目录: `{path}`")
                                st.rerun()
                    else:
                        st.warning("未检测到ComfyUI工作流目录，请手动选择")
            
            # 手动输入路径
            st.markdown("---")
            manual_path = st.text_input(
                "或手动输入目录路径",
                placeholder="粘贴或输入ComfyUI工作流目录的完整路径",
                help="例如: C:\\Users\\<USER>\\Documents\\ComfyUI\\user\\workflows"
            )
            
            if manual_path and st.button("✅ 使用此路径", use_container_width=True):
                if os.path.exists(manual_path) and os.path.isdir(manual_path):
                    st.session_state.comfyui_workflow_dir = manual_path
                    st.success(f"已设置目录: `{manual_path}`")
                    st.rerun()
                else:
                    st.error("路径不存在或不是有效目录，请检查路径是否正确")
    
    # 同步按钮和刷新按钮
    col1, col2, col3 = st.columns([2, 1, 1])
    
    with col1:
        st.markdown("#### 工作流列表")
    
    with col2:
        if st.button("🔄 同步工作流", use_container_width=True, 
                    disabled=(st.session_state.comfyui_workflow_dir == ""),
                    help="从指定目录同步工作流" if st.session_state.comfyui_workflow_dir else "请先设置工作流目录"):
            if st.session_state.comfyui_workflow_dir:
                try:
                    sync_result = sync_api_request("POST", "/workflows/sync", 
                                  headers=get_auth_headers(), 
                                  json={"workflow_dir": st.session_state.comfyui_workflow_dir})
                    
                    if sync_result.get("success") != False:
                        processed = sync_result.get("synced_count", 0)
                        st.success(f"✅ 同步完成！共处理 {processed} 个工作流")
                        st.rerun()
                    else:
                        error_msg = sync_result.get("error", "未知错误")
                        st.error(f"同步失败: {error_msg}")
                except Exception as e:
                    st.error(f"同步过程中出错: {str(e)}")
    
    with col3:
        if st.button("🔄 刷新列表", use_container_width=True):
            st.rerun()
    
    # 获取并显示工作流列表
    try:
        grouped_workflows_result = sync_api_request("GET", "/workflows/grouped", headers=get_auth_headers())
        
        # 检查是否是错误响应
        if isinstance(grouped_workflows_result, dict) and grouped_workflows_result.get("success") == False:
            st.error("获取工作流列表失败")
        elif isinstance(grouped_workflows_result, dict):
            # 后端返回按目录分组的工作流字典
            grouped_workflows = grouped_workflows_result
            
            if grouped_workflows:
                # 创建选项卡
                tab1, tab2 = st.tabs(["� 按目录分组", "🏷️ 按标签分组"])
                
                with tab1:
                    # 按目录分组显示
                    for directory, workflows in grouped_workflows.items():
                        if directory == "根目录" or directory == "":
                            st.markdown("### 📂 根目录")
                        else:
                            # 显示完整的目录路径
                            dir_display_name = directory if directory else "未分类"
                            st.markdown(f"### 📁 {dir_display_name}")
                        
                        # 使用与主页相同的网格布局显示工作流
                        display_workflows_in_management_grid(workflows, show_edit_button=True, key_prefix=f"dir_{directory.replace('/', '_').replace('\\', '_')}")
                
                with tab2:
                    # 按标签分组显示（收集所有工作流）
                    all_workflows = []
                    for workflows in grouped_workflows.values():
                        all_workflows.extend(workflows)
                    
                    dev_workflows = [w for w in all_workflows if w.get("metadata", {}).get("tags", []) and "DEV" in w["metadata"]["tags"]]
                    rdy_workflows = [w for w in all_workflows if w.get("metadata", {}).get("tags", []) and "RDY" in w["metadata"]["tags"]]
                    
                    if rdy_workflows:
                        st.markdown("### ✅ 可用工作流 (RDY)")
                        display_workflows_in_management_grid(rdy_workflows, show_edit_button=True, key_prefix="rdy")
                    
                    if dev_workflows:
                        st.markdown("### 🔧 开发中工作流 (DEV)")
                        display_workflows_in_management_grid(dev_workflows, show_edit_button=True, key_prefix="dev")
            else:
                st.info("📂 暂无工作流，请先同步工作流目录")
        else:
            st.error("获取工作流列表失败")
    except Exception as e:
        st.error(f"获取工作流列表时出错: {str(e)}")

def show_workflow_config_page():
    """显示工作流配置页面"""
    # 检查登录状态
    if "logged_in" not in st.session_state or not st.session_state.logged_in:
        st.warning("⚠️ 请先登录才能使用工作流配置功能")
        if st.button("🔙 返回主页"):
            st.session_state.page = "home"
            st.rerun()
        return
    
    # 检查是否选择了工作流
    if "selected_workflow" not in st.session_state:
        st.error("❌ 未选择工作流")
        if st.button("🔙 返回工作流列表"):
            st.session_state.page = "workflows"
            st.rerun()
        return
    
    workflow = st.session_state.selected_workflow
    
    # 安全获取工作流JSON数据
    workflow_json = safe_get_workflow_attr(workflow, 'workflow_json', {})
    
    # 如果workflow_json为None，设为空字典
    if workflow_json is None:
        workflow_json = {}
    
    # 页面标题
    st.title("🎯 工作流配置")
    
    # 返回按钮
    col1, col2 = st.columns([1, 5])
    with col1:
        if st.button("🔙 返回", use_container_width=True):
            st.session_state.page = "workflows"
            st.rerun()
    
    with col2:
        workflow_name = safe_get_workflow_attr(workflow, 'metadata.name', '未知工作流')
        st.markdown(f"### {workflow_name}")
    
    # 工作流基本信息
    with st.expander("📋 工作流基本信息", expanded=False):
        col1, col2 = st.columns(2)
        
        with col1:
            st.markdown("**名称:**")
            st.text(workflow_name)
            
            st.markdown("**类型:**")
            workflow_type = wu_classify_type(workflow_json)
            st.text(workflow_type)
            
        with col2:
            st.markdown("**标签:**")
            tags = safe_get_workflow_attr(workflow, 'metadata.tags', [])
            st.text(", ".join(tags) if tags else "无标签")
            
            st.markdown("**文件路径:**")
            file_path = safe_get_workflow_attr(workflow, 'file_path', '')
            st.text(file_path if file_path else "未知路径")
    
    # 工作流简介
    with st.expander("📖 工作流简介", expanded=True):
        notes_content = wu_extract_notes(workflow_json)
        if notes_content and notes_content != "暂无简介信息":
            st.markdown(notes_content)
        else:
            # 显示基本的工作流描述
            description = safe_get_workflow_attr(workflow, 'metadata.description', '')
            if description:
                st.markdown(description)
            else:
                st.info("暂无简介信息，请在ComfyUI中为工作流添加Note或Markdown Note节点来提供说明")
        
        # 显示工作流统计信息
        if workflow_json and isinstance(workflow_json, dict):
            node_count = len(workflow_json)
            st.markdown(f"**工作流统计:** 共包含 {node_count} 个节点")
        
        # 更全面的调试信息
        with st.expander("🔍 调试（节点概览 + 完整 JSON）", expanded=False):
            st.write("工作流基本信息:")
            st.write({
                "key": safe_get_workflow_attr(workflow, 'key', 'unknown'),
                "file_path": safe_get_workflow_attr(workflow, 'file_path', 'unknown'),
                "relative_path": safe_get_workflow_attr(workflow, 'relative_path', 'unknown'),
                "parent_dir": safe_get_workflow_attr(workflow, 'parent_dir', 'unknown')
            })

            if isinstance(workflow_json, dict) and workflow_json:
                st.markdown("**节点概览：**")
                for nid, n in list(workflow_json.items()):
                    if not isinstance(n, dict):
                        continue
                    title = n.get("_meta", {}).get("title", "")
                    ctype = n.get("class_type", "")
                    ins = n.get("inputs", {}) or {}
                    scalar_count = sum(1 for v in ins.values() if not isinstance(v, list))
                    st.write({
                        "node_id": nid,
                        "title": title,
                        "class_type": ctype,
                        "inputs_scalar_count": scalar_count,
                        "has_widgets_values": isinstance(n.get("widgets_values"), list),
                    })

                st.markdown("**完整工作流 JSON（只读）：**")
                st.json(workflow_json)
            else:
                st.warning("Workflow JSON 为空或无效")
    
    # 分析工作流节点
    nodes_analysis = wu_analyze_nodes(workflow_json)
    editable_nodes = nodes_analysis["editable_nodes"]
    
    # 关键参数设置
    st.markdown("---")
    st.subheader("⚙️ 关键参数设置")
    
    if editable_nodes:
        # 初始化参数值存储
        if "workflow_params" not in st.session_state:
            st.session_state.workflow_params = {}
        
        # 安全获取工作流key
        workflow_key = safe_get_workflow_attr(workflow, 'key', 'unknown')
        
        if workflow_key not in st.session_state.workflow_params:
            st.session_state.workflow_params[workflow_key] = {}
        
        # 为每个可编辑节点创建一个标签页式的卡片
        for i, node in enumerate(editable_nodes):
            node_id = node["id"]
            node_title = node["title"]
            class_type = node["class_type"]
            editable_params = node["editable_params"]
            
            # 检查是否是开发模式工作流
            tags = safe_get_workflow_attr(workflow, 'metadata.tags', [])
            is_dev_workflow = "DEV" in tags
            
            with st.expander(f"🔧 {node_title} ({class_type})", expanded=False):
                # 风险警告和启用开关
                enable_key = f"{workflow_key}_node_{node_id}_enable"
                
                if is_dev_workflow:
                    st.warning("⚠️ 这是开发中的工作流，某些参数可能不稳定，编辑时请谨慎")
                
                st.warning("⚠️ 编辑节点参数有风险，可能导致工作流运行失败，确认要编辑吗？")
                enable_edit = st.checkbox(
                    "确认启用编辑",
                    key=enable_key,
                    help="勾选后才能编辑此节点的参数"
                )
                
                if enable_edit:
                    st.success("✅ 编辑模式已启用")
                    
                    # 简化的参数编辑器
                    for j, param_info in enumerate(editable_params):
                        param_path = param_info["path"]
                        param_name = param_info["name"]
                        param_key = f"{workflow_key}_{param_path}"
                        current_value = param_info["current_value"]
                        
                        # 根据参数类型显示不同的控件
                        if isinstance(current_value, str):
                            if len(current_value) > 50:
                                new_value = st.text_area(
                                    param_name,
                                    value=current_value,
                                    key=param_key,
                                    height=100
                                )
                            else:
                                new_value = st.text_input(
                                    param_name,
                                    value=current_value,
                                    key=param_key
                                )
                        elif isinstance(current_value, (int, float)):
                            new_value = st.number_input(
                                param_name,
                                value=current_value,
                                key=param_key
                            )
                        elif isinstance(current_value, bool):
                            new_value = st.checkbox(
                                param_name,
                                value=current_value,
                                key=param_key
                            )
                        else:
                            new_value = st.text_input(
                                param_name,
                                value=str(current_value),
                                key=param_key
                            )
                        
                        # 存储参数值
                        st.session_state.workflow_params[workflow_key][param_path] = new_value
                else:
                    st.info("请勾选上方的确认选项以启用编辑模式")
                    
                    # 显示参数概览（只读）
                    st.markdown("**可编辑参数预览:**")
                    for param_info in editable_params:
                        param_name = param_info["name"]
                        current_value = param_info["current_value"]
                        st.markdown(f"- **{param_name}**: `{current_value}`")
    else:
        st.info("🔍 该工作流暂无可识别的可编辑参数")

    # 汇总参数与操作
    workflow_key = safe_get_workflow_attr(workflow, 'key', 'unknown')
    current_overrides = st.session_state.get("workflow_params", {}).get(workflow_key, {})

    with st.expander("🧩 参数覆盖预览与导出", expanded=False):
        st.write({"已覆盖参数数": len(current_overrides)})
        if current_overrides:
            st.json(current_overrides)

        col_p1, col_p2 = st.columns(2)
        with col_p1:
            if st.button("📝 预览修改后的工作流 JSON", key=f"preview_{workflow_key}"):
                try:
                    resp = sync_api_request(
                        "POST",
                        f"/workflows/{workflow_key}/preview",
                        headers=get_auth_headers(),
                        json={"parameters": current_overrides}
                    )
                    if isinstance(resp, dict) and resp.get("updated_workflow"):
                        st.session_state[f"preview_json_{workflow_key}"] = resp["updated_workflow"]
                        st.success("已生成预览 JSON，见下方")
                    else:
                        st.warning("预览失败或返回内容为空")
                except Exception as e:
                    st.error(f"预览失败: {e}")
        with col_p2:
            if st.button("🧹 清空已覆盖参数", key=f"clear_params_{workflow_key}"):
                st.session_state.workflow_params[workflow_key] = {}
                st.success("已清空参数覆盖")
                st.rerun()

        preview_key = f"preview_json_{workflow_key}"
        if preview_key in st.session_state:
            st.markdown("**预览 JSON（只读）**")
            st.json(st.session_state[preview_key])
    
    # 输入输出目录设置
    st.markdown("---")
    st.subheader("📁 输入输出目录设置")
    
    workflow_type = wu_classify_type(workflow_json)
    
    # 安全获取工作流key和默认目录
    workflow_key = safe_get_workflow_attr(workflow, 'key', 'unknown')
    default_input_dir = safe_get_workflow_attr(workflow, 'metadata.default_input_dir', '')
    default_output_dir = safe_get_workflow_attr(workflow, 'metadata.default_output_dir', '')
    
    # 根据工作流类型显示不同的设置选项
    if workflow_type in ["图生图", "图片编辑", "图生视频"]:
        # 需要图片输入的工作流
        col1, col2 = st.columns(2)
        
        with col1:
            st.markdown("**📥 输入图片目录**")
            
            input_dir_key = f"{workflow_key}_input_dir"
            if input_dir_key not in st.session_state:
                st.session_state[input_dir_key] = default_input_dir
            
            current_input_dir = st.text_input(
                "输入目录路径",
                value=st.session_state[input_dir_key],
                key=f"{input_dir_key}_input",
                help="将需要处理的图片放在此目录中"
            )
            
            col1_1, col1_2 = st.columns(2)
            with col1_1:
                if st.button("📂 浏览选择", key=f"{input_dir_key}_browse"):
                    selected_dir = select_folder_with_dialog()
                    if selected_dir:
                        st.session_state[input_dir_key] = selected_dir
                        st.rerun()
            
            with col1_2:
                if st.button("📁 创建目录", key=f"{input_dir_key}_create"):
                    if current_input_dir:
                        try:
                            os.makedirs(current_input_dir, exist_ok=True)
                            st.success(f"目录已创建: {current_input_dir}")
                            st.session_state[input_dir_key] = current_input_dir
                        except Exception as e:
                            st.error(f"创建目录失败: {str(e)}")
            
            # 显示目录状态
            if current_input_dir and os.path.exists(current_input_dir):
                try:
                    files = os.listdir(current_input_dir)
                    image_files = [f for f in files if f.lower().endswith(('.png', '.jpg', '.jpeg', '.bmp', '.tiff', '.webp'))]
                    st.success(f"✅ 目录存在，包含 {len(image_files)} 个图片文件")
                except:
                    st.warning("⚠️ 无法读取目录内容")
            elif current_input_dir:
                st.warning("⚠️ 目录不存在，请创建或选择有效目录")
        
        with col2:
            st.markdown("**📤 输出目录**")
            
            output_dir_key = f"{workflow_key}_output_dir"
            if output_dir_key not in st.session_state:
                st.session_state[output_dir_key] = default_output_dir
            
            current_output_dir = st.text_input(
                "输出目录路径",
                value=st.session_state[output_dir_key],
                key=f"{output_dir_key}_input",
                help="处理后的图片/视频将保存在此目录中"
            )
            
            col2_1, col2_2 = st.columns(2)
            with col2_1:
                if st.button("📂 浏览选择", key=f"{output_dir_key}_browse"):
                    selected_dir = select_folder_with_dialog()
                    if selected_dir:
                        st.session_state[output_dir_key] = selected_dir
                        st.rerun()
            
            with col2_2:
                if st.button("📁 创建目录", key=f"{output_dir_key}_create"):
                    if current_output_dir:
                        try:
                            os.makedirs(current_output_dir, exist_ok=True)
                            st.success(f"目录已创建: {current_output_dir}")
                            st.session_state[output_dir_key] = current_output_dir
                        except Exception as e:
                            st.error(f"创建目录失败: {str(e)}")
            
            # 显示目录状态
            if current_output_dir and os.path.exists(current_output_dir):
                st.success("✅ 输出目录已就绪")
            elif current_output_dir:
                st.warning("⚠️ 目录不存在，请创建或选择有效目录")
    
    elif workflow_type in ["文生图", "文生视频"]:
        # 只需要输出目录的工作流
        st.markdown("**📤 输出目录设置**")
        
        output_type = "视频" if workflow_type == "文生视频" else "图片"
        st.info(f"此工作流会生成{output_type}，请设置输出目录")
        
        output_dir_key = f"{workflow_key}_output_dir"
        if output_dir_key not in st.session_state:
            st.session_state[output_dir_key] = default_output_dir
        
        current_output_dir = st.text_input(
            f"输出目录路径",
            value=st.session_state[output_dir_key],
            key=f"{output_dir_key}_input",
            help=f"生成的{output_type}将保存在此目录中"
        )
        
        col1, col2, col3 = st.columns(3)
        with col1:
            if st.button("📂 浏览选择", key=f"{output_dir_key}_browse"):
                selected_dir = select_folder_with_dialog()
                if selected_dir:
                    st.session_state[output_dir_key] = selected_dir
                    st.rerun()
        
        with col2:
            if st.button("📁 创建目录", key=f"{output_dir_key}_create"):
                if current_output_dir:
                    try:
                        os.makedirs(current_output_dir, exist_ok=True)
                        st.success(f"目录已创建: {current_output_dir}")
                        st.session_state[output_dir_key] = current_output_dir
                    except Exception as e:
                        st.error(f"创建目录失败: {str(e)}")
        
        with col3:
            if current_output_dir and os.path.exists(current_output_dir):
                if st.button("📂 打开目录", key=f"{output_dir_key}_open"):
                    try:
                        if os.name == "nt":  # Windows
                            os.startfile(current_output_dir)
                        else:  # Linux/Mac
                            import subprocess
                            subprocess.run(["xdg-open", current_output_dir])
                        st.success("已在文件管理器中打开目录")
                    except:
                        st.warning("无法打开目录")
        
        # 显示目录状态
        if current_output_dir and os.path.exists(current_output_dir):
            st.success("✅ 输出目录已就绪")
        elif current_output_dir:
            st.warning("⚠️ 目录不存在，请创建或选择有效目录")
    
    else:
        st.info("🔍 工作流类型未明确，请手动设置输入输出目录")
    
    # 运行控制
    st.markdown("---")
    st.subheader("🚀 运行控制")
    
    # 检查工作流状态
    is_dev_workflow = False
    if isinstance(workflow, dict):
        tags = workflow.get('metadata', {}).get('tags', [])
        is_dev_workflow = "DEV" in tags
    else:
        metadata = getattr(workflow, 'metadata', {})
        if hasattr(metadata, 'tags'):
            is_dev_workflow = "DEV" in metadata.tags
        elif isinstance(metadata, dict):
            is_dev_workflow = "DEV" in metadata.get('tags', [])
    
    if is_dev_workflow:
        st.warning("⚠️ 这是开发中的工作流，仅供查看，无法运行")
        st.button("▶️ 开始运行", disabled=True, help="开发中的工作流无法运行")
    else:
        col1, col2, col3 = st.columns(3)
        
        with col1:
            if st.button("▶️ 开始运行", type="primary", use_container_width=True):
                overrides = st.session_state.get("workflow_params", {}).get(workflow_key, {})
                st.info("正在提交运行请求…")
                try:
                    payload = {
                        "workflow_key": workflow_key,
                        "parameters": overrides,
                        "input_dir": st.session_state.get(f"{workflow_key}_input_dir"),
                        "output_dir": st.session_state.get(f"{workflow_key}_output_dir")
                    }
                    resp = sync_api_request("POST", "/run", headers=get_auth_headers(), json=payload)
                    # 若后台返回 TaskInfo 结构
                    if isinstance(resp, dict) and resp.get("task_id"):
                        st.success(f"已创建任务: {resp['task_id']}")
                    else:
                        st.warning("未获取到任务信息，请检查后台日志")
                except Exception as e:
                    st.error(f"运行提交失败: {e}")
        
        with col2:
            if st.button("💾 保存到工作流文件", use_container_width=True):
                overrides = st.session_state.get("workflow_params", {}).get(workflow_key, {})
                if not overrides:
                    st.info("当前没有可保存的参数变更")
                try:
                    payload = {"parameters": overrides}
                    resp = sync_api_request("POST", f"/workflows/{workflow_key}/save", headers=get_auth_headers(), json=payload)
                    if isinstance(resp, dict) and resp.get("success"):
                        data = resp.get("data", {})
                        fp = data.get("file_path", "")
                        st.success(f"已保存到文件: {fp}")
                        # 标记参数已保存，激活重新载入按钮
                        if "workflow_saved_status" not in st.session_state:
                            st.session_state.workflow_saved_status = {}
                        st.session_state.workflow_saved_status[workflow_key] = True
                    else:
                        st.warning("保存未成功，请检查后端日志")
                except Exception as e:
                    st.error(f"保存失败: {e}")

        with col3:
            # 检查是否有参数变更（无论是否已保存）
            current_overrides = st.session_state.get("workflow_params", {}).get(workflow_key, {})
            saved_status = st.session_state.get("workflow_saved_status", {}).get(workflow_key, False)

            # 按钮状态：如果有未保存的参数变更则显示警告色，否则正常
            button_type = "secondary" if current_overrides and not saved_status else "primary"

            if st.button("🔄 重新载入工作流", use_container_width=True, type=button_type):
                try:
                    # 重新获取工作流数据以载入最新参数，使用force_reload强制从磁盘读取
                    resp = sync_api_request("GET", f"/workflows/{workflow_key}?force_reload=true", headers=get_auth_headers())
                    if isinstance(resp, dict) and resp.get("success"):
                        # 清除当前的参数覆盖，让界面显示最新的工作流参数
                        if "workflow_params" in st.session_state and workflow_key in st.session_state.workflow_params:
                            del st.session_state.workflow_params[workflow_key]

                        # 清除保存状态标记
                        if "workflow_saved_status" in st.session_state and workflow_key in st.session_state.workflow_saved_status:
                            del st.session_state.workflow_saved_status[workflow_key]

                        # 如果有未保存的参数，给出警告
                        if current_overrides and not saved_status:
                            st.warning("⚠️ 已重新载入工作流，之前未保存的参数修改已丢失")
                        else:
                            st.success("✅ 已重新载入最新的工作流参数")
                        st.rerun()
                    else:
                        st.error("重新载入失败，请检查后端连接")
                        # 显示详细的响应信息用于调试
                        if isinstance(resp, dict):
                            st.error(f"API响应: {resp}")
                        else:
                            st.error(f"响应类型: {type(resp)}, 内容: {resp}")
                except Exception as e:
                    st.error(f"重新载入失败: {e}")
                    import traceback
                    st.error(f"详细错误: {traceback.format_exc()}")

def safe_get_workflow_attr(workflow, attr_path, default=None):
    """安全获取工作流属性，支持dict和对象两种格式"""
    try:
        # 按路径分割属性
        attrs = attr_path.split('.')
        current = workflow
        
        for attr in attrs:
            if isinstance(current, dict):
                current = current.get(attr, default)
            else:
                current = getattr(current, attr, default)
            
            if current is None:
                return default
        
        return current
    except:
        return default

def extract_notes_from_workflow(workflow_json: Dict[str, Any]) -> str:
    """从工作流JSON中提取 Note/Markdown 说明（避免误把 Prompt 当简介）。"""
    if not isinstance(workflow_json, dict):
        return "暂无简介信息"

    notes: List[str] = []

    for _, node in workflow_json.items():
        if not isinstance(node, dict):
            continue
        class_type = str(node.get("class_type", ""))
        title = str(node.get("_meta", {}).get("title", ""))

        # 只将明确的 Note/Markdown 节点当作简介来源
        is_note_like = (
            any(k in class_type.lower() for k in ["note", "markdown"]) or
            any(k in title.lower() for k in ["note", "markdown", "简介", "description"])
        )
        if not is_note_like:
            continue

        inputs = node.get("inputs", {}) or {}
        for field in ["markdown", "text", "content", "note", "description", "string"]:
            val = inputs.get(field)
            if isinstance(val, str) and val.strip():
                notes.append(val.strip())
                break

        # 兜底 widgets_values（部分自定义节点将文本存在这里）
        wv = node.get("widgets_values")
        if isinstance(wv, list) and wv and isinstance(wv[0], str) and wv[0].strip():
            notes.append(wv[0].strip())

    # 去重并拼接
    notes = list(dict.fromkeys(notes))
    return "\n\n".join(notes) if notes else "暂无简介信息"

def classify_workflow_type(workflow_json: Dict[str, Any]) -> str:
    """根据工作流内容分类工作流类型"""
    has_image_input = False
    has_text_input = False
    has_video_output = False
    has_image_output = False
    
    for node_id, node_data in workflow_json.items():
        if isinstance(node_data, dict):
            class_type = node_data.get("class_type", "").lower()
            
            # 检查输入类型
            if "loadimage" in class_type or "imageload" in class_type:
                has_image_input = True
            elif "cliptext" in class_type or "textinput" in class_type:
                has_text_input = True
            
            # 检查输出类型
            if "videosave" in class_type or "videoout" in class_type or "animatediff" in class_type:
                has_video_output = True
            elif "imagesave" in class_type or "imageout" in class_type:
                has_image_output = True
    
    # 分类逻辑
    if has_image_input and has_image_output:
        return "图生图"
    elif has_image_input and has_video_output:
        return "图生视频"
    elif has_text_input and has_video_output:
        return "文生视频"
    elif has_text_input and has_image_output:
        return "文生图"
    else:
        return "其他"

def analyze_workflow_nodes(workflow_json: Dict[str, Any]) -> Dict[str, List[Dict]]:
    """分析工作流节点，提取可编辑的参数（更宽松，覆盖更多关键节点）。"""
    editable_nodes: List[Dict] = []

    if not isinstance(workflow_json, dict):
        return {"editable_nodes": editable_nodes}

    for node_id, node_data in workflow_json.items():
        if not isinstance(node_data, dict):
            continue
        class_type = node_data.get("class_type", "")
        node_title = node_data.get("_meta", {}).get("title", class_type)
        inputs = node_data.get("inputs", {}) or {}

        # 排除显然不需要编辑的节点（加载/保存/说明等）
        if is_excluded_node(class_type):
            continue

        editable_params = extract_editable_params_from_node(str(node_id), class_type, node_title, inputs)
        if editable_params:
            editable_nodes.append({
                "id": str(node_id),
                "title": node_title,
                "class_type": class_type,
                "inputs": inputs,
                "editable_params": editable_params,
            })

    return {"editable_nodes": editable_nodes}

def is_excluded_node(class_type: str) -> bool:
    """判断是否为不需要编辑的节点类型（加载/保存/说明/路由等）。"""
    if not class_type:
        return True
    ct = class_type.lower()
    excluded_keywords = [
        "checkpointloader", "loader", "vae", "controlnet", "lora", "unet",
        "note", "markdown", "comment", "group", "reroute", "save", "preview", "viewer",
        "decode", "emptylatent", "image save", "output"
    ]
    return any(k in ct for k in excluded_keywords)

def extract_editable_params_from_node(node_id: str, class_type: str, node_title: str, inputs: Dict) -> List[Dict]:
    """从节点中提取可编辑参数"""
    editable_params = []
    
    if not inputs or not isinstance(inputs, dict):
        return editable_params
    
    for input_name, input_value in inputs.items():
        # 排除连接类型的输入（通常是列表格式）
        if isinstance(input_value, list):
            continue
        
        # 文本类型参数
        if isinstance(input_value, str):
            # 文本输入参数
            if any(keyword in input_name.lower() for keyword in ["text", "prompt", "description", "content", "string"]):
                editable_params.append({
                    "name": f"{input_name} (文本)",
                    "path": f"{node_id}.inputs.{input_name}",
                    "current_value": input_value,
                    "type": "text"
                })
            # 选择类型参数
            elif any(keyword in input_name.lower() for keyword in ["sampler", "scheduler", "method", "mode"]):
                editable_params.append({
                    "name": f"{input_name} (选择)",
                    "path": f"{node_id}.inputs.{input_name}",
                    "current_value": input_value,
                    "type": "select"
                })
        
        # 数值类型参数
        elif isinstance(input_value, (int, float)):
            # 所有数值参数都可编辑（更宽松的策略）
            param_type = "integer" if isinstance(input_value, int) else "float"
            editable_params.append({
                "name": f"{input_name} (数值)",
                "path": f"{node_id}.inputs.{input_name}",
                "current_value": input_value,
                "type": param_type
            })

        # 布尔类型参数
        elif isinstance(input_value, bool):
            editable_params.append({
                "name": f"{input_name} (开关)",
                "path": f"{node_id}.inputs.{input_name}",
                "current_value": input_value,
                "type": "boolean"
            })
    
    return editable_params

def show_workflow_detail_page():
    """显示工作流详情页面"""
    st.title("🎯 工作流详情")
    
    if "selected_workflow" not in st.session_state:
        st.error("未选择工作流")
        if st.button("返回工作流列表"):
            st.session_state.page = "workflows"
            st.rerun()
        return
    
    workflow = st.session_state.selected_workflow
    
    # 工作流基本信息
    st.subheader(workflow["name"])
    description = workflow.get("description", "无描述")
    tags = ", ".join(workflow.get("tags", []))
    st.markdown(f"**描述:** {description}")
    st.markdown(f"**标签:** {tags}")
    
    # 参数设置
    st.markdown("---")
    st.subheader("⚙️ 参数设置")
    
    # 这里可以添加参数编辑界面
    st.info("参数编辑功能开发中...")
    
    # 运行控制
    st.markdown("---")
    st.subheader("🚀 运行控制")
    
    col1, col2 = st.columns(2)
    with col1:
        if st.button("▶️ 开始运行", type="primary", use_container_width=True):
            st.info("运行功能开发中...")
    
    with col2:
        if st.button("🔙 返回列表", use_container_width=True):
            st.session_state.page = "workflows"
            st.rerun()
    """显示工作流详情页面"""
    st.title("🎯 工作流详情")
    
    if "selected_workflow" not in st.session_state:
        st.error("未选择工作流")
        if st.button("返回工作流列表"):
            st.session_state.page = "workflows"
            st.rerun()
        return
    
    workflow = st.session_state.selected_workflow
    
    # 工作流基本信息
    st.subheader(workflow["name"])
    description = workflow.get("description", "无描述")
    tags = ", ".join(workflow.get("tags", []))
    st.markdown(f"**描述:** {description}")
    st.markdown(f"**标签:** {tags}")
    
    # 参数设置
    st.markdown("---")
    st.subheader("⚙️ 参数设置")
    
    # 这里可以添加参数编辑界面
    st.info("参数编辑功能开发中...")
    
    # 运行控制
    st.markdown("---")
    st.subheader("🚀 运行控制")
    
    col1, col2 = st.columns(2)
    with col1:
        if st.button("▶️ 开始运行", type="primary", use_container_width=True):
            st.info("运行功能开发中...")
    
    with col2:
        if st.button("🔙 返回列表", use_container_width=True):
            st.session_state.page = "workflows"
            st.rerun()

def show_pipeline_page():
    """显示流水线编排页面"""
    st.title("🔗 流水线编排")
    
    st.info("流水线编排功能开发中...")
    if st.button("返回主页"):
        st.session_state.page = "home"
        st.rerun()

def main():
    """主函数"""
    # 初始化页面状态
    if "page" not in st.session_state:
        st.session_state.page = "home"
    
    # 侧边栏导航
    with st.sidebar:
        st.title("🧭 导航")
        
        # 显示登录状态
        if "logged_in" in st.session_state and st.session_state.logged_in:
            username = st.session_state.user_info.get("username", "用户")
            st.success(f"✅ 已登录: {username}")
        else:
            st.error("❌ 未登录")
        
        st.markdown("---")
        
        if st.button("🏠 主页", use_container_width=True):
            st.session_state.page = "home"
            st.rerun()
        
        # 检查登录状态显示不同的导航选项
        if "logged_in" in st.session_state and st.session_state.logged_in:
            if st.button("⚙️ 工作流管理", use_container_width=True):
                st.session_state.page = "workflows"
                st.rerun()
            
            if st.button("🔗 流水线编排", use_container_width=True):
                st.session_state.page = "pipeline"
                st.rerun()
            
            if st.button("📊 任务监控", use_container_width=True):
                st.session_state.page = "monitor"
                st.rerun()
            
            if st.button("📁 输出管理", use_container_width=True):
                st.session_state.page = "outputs"
                st.rerun()
        else:
            # 未登录时显示占位按钮
            st.button("⚙️ 工作流管理", use_container_width=True, disabled=True, help="请先登录")
            st.button("🔗 流水线编排", use_container_width=True, disabled=True, help="请先登录") 
            st.button("📊 任务监控", use_container_width=True, disabled=True, help="请先登录")
            st.button("📁 输出管理", use_container_width=True, disabled=True, help="请先登录")
    
    # 页面路由
    if st.session_state.page == "home":
        show_home_page()
    elif st.session_state.page == "workflows":
        show_workflows_page()
    elif st.session_state.page == "workflow_detail":
        show_workflow_detail_page()
    elif st.session_state.page == "workflow_config":
        show_workflow_config_page()
    elif st.session_state.page == "workflow_editor":
        st.title("✏️ 工作流编辑器")
        st.info("编辑器功能开发中...")
    elif st.session_state.page == "pipeline":
        show_pipeline_page()
    elif st.session_state.page == "monitor":
        st.title("📊 任务监控")
        st.info("监控功能开发中...")
    elif st.session_state.page == "outputs":
        st.title("📁 输出管理")
        st.info("输出管理功能开发中...")
    else:
        show_home_page()

if __name__ == "__main__":
    main()
