#!/usr/bin/env python3
"""
Test workflow reading functionality
"""
import sys
from pathlib import Path

# Add the app directory to the path
sys.path.insert(0, str(Path(__file__).parent))

from app.services.workflow_service import workflow_service

def test_workflow_reading():
    """Test reading workflows"""
    print("🧪 Testing Workflow Reading")
    print("=" * 50)
    
    # Test problematic workflow
    workflow_key = "RDY_pattern-extraction"
    
    print(f"📖 Testing workflow: {workflow_key}")
    
    try:
        workflow = workflow_service.get_workflow_local(workflow_key)
        if workflow:
            print(f"✅ Successfully loaded workflow: {workflow.metadata.name}")
            print(f"📁 File path: {workflow.file_path}")
            print(f"🔢 Node count: {len(workflow.workflow_json) if workflow.workflow_json else 0}")
        else:
            print(f"❌ Failed to load workflow: {workflow_key}")
            
    except Exception as e:
        print(f"❌ Error loading workflow: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_workflow_reading()
