import os
import json
from pathlib import Path
from typing import List, Optional, Dict, Any
from datetime import datetime

from ..api.schemas import WorkflowInfo, WorkflowMetadata, WorkflowTag
from ..api.comfy_client import comfy_client


class WorkflowService:
    def __init__(self):
        # 本地工作流目录（用于测试和简单管理）
        self.workflows_dir = Path(os.getenv("WORKFLOWS_DIR", "app/config/workflows"))
        self.workflows_dir.mkdir(parents=True, exist_ok=True)
        # ComfyUI 目录（可选）
        self.comfyui_workflow_dir = None  # 将由用户指定

    def set_comfyui_workflow_dir(self, workflow_dir: str):
        """设置ComfyUI工作流目录"""
        self.comfyui_workflow_dir = workflow_dir

    def list_workflows(self) -> List[WorkflowInfo]:
        """同步获取所有工作流列表（仅本地目录，供测试使用）"""
        return self._load_local_config_workflows()

    async def list_workflows_async(self) -> List[WorkflowInfo]:
        """异步获取所有工作流列表（本地 + 可选 ComfyUI 目录）"""
        workflows: List[WorkflowInfo] = []
        workflows.extend(self._load_local_config_workflows())

        if self.comfyui_workflow_dir:
            try:
                comfy_workflows = await comfy_client.get_workflows_from_directory(self.comfyui_workflow_dir)

                for wf_data in comfy_workflows:
                    workflow_key = wf_data["name"].lower().replace(" ", "_")
                    if wf_data.get("parent_dir"):
                        dir_key = (
                            wf_data["parent_dir"].replace("\\", "_")
                            .replace("/", "_")
                            .replace(" ", "_")
                            .lower()
                        )
                        workflow_key = f"{dir_key}_{workflow_key}"

                    workflow_json = wf_data.get("workflow_json", {})
                    editable_params = self._extract_editable_params(workflow_json)
                    metadata = WorkflowMetadata(
                        name=wf_data["display_name"],
                        description=f"从ComfyUI加载的工作流: {wf_data['display_name']}",
                        tags=[WorkflowTag.RDY],
                        editable_params=editable_params,
                        default_input_dir=f"app/data/inputs/{workflow_key}",
                        default_output_dir=f"app/data/outputs/{workflow_key}",
                    )
                    workflow_info = WorkflowInfo(
                        key=workflow_key,
                        metadata=metadata,
                        workflow_json=workflow_json,
                        file_path=wf_data.get("file_path", ""),
                        relative_path=wf_data.get("relative_path", ""),
                        parent_dir=wf_data.get("parent_dir", ""),
                    )
                    workflows.append(workflow_info)
            except Exception as e:
                print(f"警告：读取ComfyUI工作流失败: {str(e)}")
        return workflows
    
    async def get_workflows_grouped_by_directory(self) -> Dict[str, List[WorkflowInfo]]:
        """获取按目录分组的工作流列表"""
        workflows = await self.list_workflows_async()
        grouped: Dict[str, List[WorkflowInfo]] = {}

        for workflow in workflows:
            # 获取父目录，空字符串和"."都视为根目录
            parent_dir = getattr(workflow, "parent_dir", "")
            if not parent_dir or parent_dir == "." or parent_dir == "":
                parent_dir = "根目录"

            if parent_dir not in grouped:
                grouped[parent_dir] = []

            grouped[parent_dir].append(workflow)

        # 对每个组内的工作流按名称排序
        for group in grouped.values():
            group.sort(key=lambda x: x.metadata.name)

        # 确保根目录排在最前面
        if "根目录" in grouped:
            root_workflows = grouped.pop("根目录")
            grouped = {"根目录": root_workflows, **grouped}

        return grouped
    
    async def get_workflow_async(self, workflow_key: str) -> Optional[WorkflowInfo]:
        """异步获取指定工作流详情"""
        # 先从本地目录查找
        local = self.get_workflow_local(workflow_key)
        if local:
            return local
            
        try:
            if not self.comfyui_workflow_dir:
                return None
            workflows = await self.list_workflows_async()
            return next((w for w in workflows if w.key == workflow_key), None)
        except Exception as e:
            print(f"警告：获取工作流 {workflow_key} 失败: {str(e)}")
            return None

    async def save_workflow(self, workflow_key: str, *, parameters: Optional[Dict[str, Any]] = None,
                            updated_workflow_json: Optional[Dict[str, Any]] = None,
                            make_backup: bool = True) -> Dict[str, Any]:
        """保存工作流到其来源 JSON 文件。
        - 如果提供 parameters，则基于磁盘中的最新 JSON 应用参数路径更新。
        - 如果提供 updated_workflow_json，直接写入该 JSON。
        - 优先使用 updated_workflow_json；两者都为空则抛错。
        返回：{"success": bool, "file_path": str, "backup_path": Optional[str], "mtime": str, "size": int, "verifications": dict}
        """
        # 1) 定位文件
        workflow = await self.get_workflow_async(workflow_key)
        if not workflow:
            raise Exception(f"工作流 {workflow_key} 不存在")

        file_path = workflow.file_path or ""
        if not file_path:
            raise Exception("该工作流没有可写入的文件路径")

        path_obj = Path(file_path)
        if not path_obj.exists():
            rel = getattr(workflow, "relative_path", None)
            candidate = (self.workflows_dir / rel) if rel else None
            if candidate and candidate.exists():
                path_obj = candidate
            else:
                raise Exception(f"找不到工作流文件: {file_path}")

        # 2) 以磁盘为准读取 JSON，增加文件完整性检查
        try:
            with open(path_obj, "r", encoding="utf-8") as rf:
                content = rf.read().strip()
                if not content:
                    raise Exception(f"工作流文件为空: {path_obj}")
                base_json = json.loads(content)
                if not isinstance(base_json, dict) or not base_json:
                    raise Exception(f"工作流文件格式无效: {path_obj}")
        except json.JSONDecodeError as e:
            raise Exception(f"工作流文件JSON格式错误: {e}")
        except Exception as e:
            raise Exception(f"读取工作流文件失败: {e}")

        # 3) 生成要写入的 JSON
        if updated_workflow_json is not None:
            new_json = updated_workflow_json
        elif parameters:
            try:
                new_json = comfy_client.update_workflow_parameters(base_json, parameters)
            except Exception as e:
                raise Exception(f"应用参数失败: {str(e)}")
        else:
            raise Exception("未提供需要保存的更新内容（parameters 或 updated_workflow_json）")

        # 3.1) 当存在 nodes 列表结构时，移除顶层的纯数字键（这些通常是旧的映射结构残留，会让 ComfyUI 忽略真正的 nodes 值）
        try:
            if isinstance(new_json, dict) and isinstance(new_json.get("nodes"), list):
                numeric_keys = [k for k in list(new_json.keys()) if k.isdigit()]
                # 可选：将这些键中的 widgets_values 合入对应节点（通常 update 已处理，这里只是兜底）
                if numeric_keys:
                    nodes = new_json.get("nodes", [])
                    node_index = {str(n.get("id")): n for n in nodes if isinstance(n, dict) and "id" in n}
                    for nk in numeric_keys:
                        try:
                            ghost = new_json.get(nk)
                            if isinstance(ghost, dict) and "widgets_values" in ghost and nk in node_index:
                                wv = ghost.get("widgets_values")
                                if isinstance(wv, list):
                                    target = node_index[nk]
                                    twv = target.get("widgets_values")
                                    if not isinstance(twv, list):
                                        target["widgets_values"] = list(wv)
                                    else:
                                        for i, val in enumerate(wv):
                                            if i < len(twv):
                                                if val is not None:
                                                    twv[i] = val
                                            else:
                                                twv.append(val)
                        except Exception:
                            pass
                        # 不论是否合并成功，删除这些顶层数字键，避免干扰
                        try:
                            del new_json[nk]
                        except Exception:
                            pass
        except Exception:
            pass

        # 4) 智能备份 + 原子替换
        backup_path: Optional[str] = None
        try:
            path_obj.parent.mkdir(parents=True, exist_ok=True)

            # 简化的备份策略：仅在文件存在且内容不同时创建单个备份
            backup_path = None
            if make_backup and path_obj.exists():
                try:
                    # 检查内容是否真的改变了
                    import hashlib
                    new_content_str = json.dumps(new_json, ensure_ascii=False, indent=2)
                    new_hash = hashlib.md5(new_content_str.encode('utf-8')).hexdigest()

                    with open(path_obj, 'r', encoding='utf-8') as f:
                        existing_content = f.read()
                        existing_hash = hashlib.md5(existing_content.encode('utf-8')).hexdigest()

                    # 只有内容真正改变时才创建一个简单备份
                    if new_hash != existing_hash:
                        backup_path = str(path_obj) + ".bak"
                        import shutil
                        shutil.copy2(str(path_obj), backup_path)
                        print(f"📁 已创建备份: {Path(backup_path).name}")
                    else:
                        print(f"ℹ️ 内容未改变，跳过备份")

                except Exception as backup_error:
                    print(f"警告：备份失败（将继续保存）：{backup_error}")
                    backup_path = None

            # 增强的原子写入：多重验证和错误恢复
            tmp_path = str(path_obj) + ".tmp"
            success = False
            attempts = 0
            max_attempts = 3

            while not success and attempts < max_attempts:
                attempts += 1
                try:
                    # 写入临时文件
                    with open(tmp_path, "w", encoding="utf-8", newline='\n') as f:
                        json.dump(new_json, f, ensure_ascii=False, indent=2)
                        f.flush()  # 强制刷新缓冲区
                        os.fsync(f.fileno())  # 强制同步到磁盘

                    # 多重验证写入的文件
                    with open(tmp_path, "r", encoding="utf-8") as f:
                        verify_content = f.read().strip()
                        if not verify_content:
                            raise Exception("写入的临时文件为空")

                        verify_json = json.loads(verify_content)  # 验证JSON格式
                        if not isinstance(verify_json, dict) or not verify_json:
                            raise Exception("写入的JSON格式无效")

                        # 验证关键节点是否存在
                        if len(verify_json) != len(new_json):
                            raise Exception(f"节点数量不匹配: 期望{len(new_json)}, 实际{len(verify_json)}")

                    # 验证通过，执行原子替换
                    os.replace(tmp_path, str(path_obj))

                    # 最终验证替换后的文件
                    with open(path_obj, "r", encoding="utf-8") as f:
                        final_content = f.read().strip()
                        if not final_content:
                            raise Exception("替换后的文件为空")
                        json.loads(final_content)

                    success = True
                    print(f"✅ 文件写入成功 (尝试 {attempts}/{max_attempts})")

                except Exception as write_error:
                    print(f"⚠️ 写入尝试 {attempts}/{max_attempts} 失败: {write_error}")

                    # 清理临时文件
                    if os.path.exists(tmp_path):
                        try:
                            os.remove(tmp_path)
                        except:
                            pass

                    if attempts >= max_attempts:
                        raise Exception(f"写入文件失败 (已尝试{max_attempts}次): {write_error}")

                    # 短暂等待后重试
                    import time
                    time.sleep(0.1)

            # 5) 轻量校验
            verifications: Dict[str, Any] = {}
            try:
                with open(path_obj, "r", encoding="utf-8") as vf:
                    verify_json = json.load(vf)
                verify_notes: List[str] = []
                for p, v in (parameters or {}).items():
                    parts = [pp for pp in str(p).split('.') if pp]
                    if len(parts) >= 3 and parts[1] == "widgets_values":
                        if isinstance(verify_json, dict) and isinstance(verify_json.get("nodes"), list):
                            nid = parts[0]
                            idx = int(parts[2])
                            node = next((n for n in verify_json.get("nodes", []) if str(n.get("id")) == str(nid)), None)
                            if node is not None:
                                wv = node.get("widgets_values", [])
                                actual = wv[idx] if len(wv) > idx else None
                                verifications[p] = {"expected": v, "actual": actual, "ok": actual == v}
                                if actual != v:
                                    verify_notes.append(f"{p} 未生效(期望 {v})")
                    elif parts:
                        # 映射结构
                        cur: Any = verify_json
                        ok = True
                        for seg in parts[:-1]:
                            if isinstance(cur, list):
                                try:
                                    cur = cur[int(seg)]
                                except Exception:
                                    ok = False
                                    break
                            else:
                                cur = cur.get(seg) if isinstance(cur, dict) else None
                                if cur is None:
                                    ok = False
                                    break
                        if ok:
                            last = parts[-1]
                            if isinstance(cur, list):
                                try:
                                    actual = cur[int(last)]
                                except Exception:
                                    actual = None
                            else:
                                actual = cur.get(last) if isinstance(cur, dict) else None
                            verifications[p] = {"expected": v, "actual": actual, "ok": actual == v}
                            if actual != v:
                                verify_notes.append(f"{p} 未生效(期望 {v})")
                if verify_notes:
                    print("保存校验提示：" + "; ".join(verify_notes))
            except Exception:
                verifications = {}

            stat = path_obj.stat()
            return {
                "success": True,
                "file_path": str(path_obj),
                "backup_path": backup_path,
                "mtime": datetime.fromtimestamp(stat.st_mtime).isoformat(),
                "size": stat.st_size,
                "verifications": verifications if (parameters or {}) else {}
            }
        except Exception as e:
            raise Exception(f"保存工作流失败: {str(e)}")

    def get_workflow(self, workflow_key: str) -> Optional[WorkflowInfo]:
        """同步获取指定工作流详情（用于简单校验）"""
        # 简化实现：同步包装异步获取或在未配置目录时直接返回 None
        import asyncio
        try:
            loop = asyncio.get_event_loop()
        except RuntimeError:
            loop = None

        if loop and loop.is_running():
            # 在已有事件循环中，直接返回 None 以避免阻塞；调用方仅做存在性检查
            return None
        return asyncio.run(self.get_workflow_async(workflow_key))

    # ========== 本地工作流 CRUD（供测试使用） ==========
    def _workflow_dir_for_key(self, key: str) -> Path:
        return self.workflows_dir / key

    def create_workflow(self, key: str, metadata: WorkflowMetadata, workflow_json: Optional[Dict[str, Any]] = None) -> WorkflowInfo:
        wf_dir = self._workflow_dir_for_key(key)
        wf_dir.mkdir(parents=True, exist_ok=True)
        # 写入 metadata.json
        metadata_path = wf_dir / "metadata.json"
        with open(metadata_path, "w", encoding="utf-8") as f:
            json.dump({
                "name": metadata.name,
                "description": metadata.description,
                "tags": [t.value if hasattr(t, 'value') else str(t) for t in (metadata.tags or [])],
                "editable_params": metadata.editable_params or {},
                "default_input_dir": metadata.default_input_dir,
                "default_output_dir": metadata.default_output_dir,
            }, f, ensure_ascii=False, indent=2)
        # 写入 workflow.json
        workflow_path = wf_dir / "workflow.json"
        with open(workflow_path, "w", encoding="utf-8") as f:
            json.dump(workflow_json or {}, f, ensure_ascii=False, indent=2)

        return WorkflowInfo(
            key=key,
            metadata=metadata,
            workflow_json=workflow_json or {},
            file_path=str(workflow_path),
            relative_path=key,
            parent_dir="本地工作流",
        )

    def get_workflow_local(self, key: str) -> Optional[WorkflowInfo]:
        wf_dir = self._workflow_dir_for_key(key)
        metadata_path = wf_dir / "metadata.json"
        workflow_path = wf_dir / "workflow.json"
        if not (metadata_path.exists() and workflow_path.exists()):
            return None
        try:
            with open(metadata_path, "r", encoding="utf-8") as f:
                metadata_data = json.load(f)
            with open(workflow_path, "r", encoding="utf-8") as f:
                workflow_json = json.load(f)

            tags = []
            for tag_str in metadata_data.get("tags", ["RDY"]):
                tags.append(WorkflowTag.DEV if tag_str == "DEV" else WorkflowTag.RDY)

            metadata = WorkflowMetadata(
                name=metadata_data.get("name", key),
                description=metadata_data.get("description", ""),
                tags=tags,
                editable_params=metadata_data.get("editable_params", {}),
                default_input_dir=metadata_data.get("default_input_dir", f"app/data/inputs/{key}"),
                default_output_dir=metadata_data.get("default_output_dir", f"app/data/outputs/{key}"),
            )
            return WorkflowInfo(
                key=key,
                metadata=metadata,
                workflow_json=workflow_json,
                file_path=str(workflow_path),
                relative_path=key,
                parent_dir="本地工作流",
            )
        except Exception as e:
            print(f"警告：读取本地工作流失败: {e}")
            return None

    def delete_workflow(self, key: str) -> bool:
        wf_dir = self._workflow_dir_for_key(key)
        if not wf_dir.exists():
            return False
        import shutil
        shutil.rmtree(wf_dir, ignore_errors=True)
        return not wf_dir.exists()

    def update_workflow(self, key: str, new_metadata: WorkflowMetadata) -> Optional[WorkflowInfo]:
        current = self.get_workflow_local(key)
        if not current:
            return None
        # 覆盖元数据
        metadata_path = self._workflow_dir_for_key(key) / "metadata.json"
        with open(metadata_path, "w", encoding="utf-8") as f:
            json.dump({
                "name": new_metadata.name,
                "description": new_metadata.description,
                "tags": [t.value if hasattr(t, 'value') else str(t) for t in (new_metadata.tags or [])],
                "editable_params": new_metadata.editable_params or {},
                "default_input_dir": new_metadata.default_input_dir,
                "default_output_dir": new_metadata.default_output_dir,
            }, f, ensure_ascii=False, indent=2)
        return self.get_workflow_local(key)
    
    def _extract_editable_params(self, workflow_json: Dict[str, Any]) -> Dict[str, Any]:
        """从工作流JSON中提取可编辑参数"""
        editable_params = {}
        
        try:
            for node_id, node_data in workflow_json.items():
                if isinstance(node_data, dict):
                    node_class = node_data.get("class_type", "")
                    node_inputs = node_data.get("inputs", {})
                    
                    # 识别常见的可编辑参数类型
                    for input_name, input_value in node_inputs.items():
                        if isinstance(input_value, str) and ("text" in input_name.lower() or "prompt" in input_name.lower()):
                            # 文本输入
                            param_name = f"{node_class} - {input_name}"
                            param_path = f"{node_id}.inputs.{input_name}"
                            editable_params[param_name] = param_path
                        elif isinstance(input_value, (int, float)):
                            # 数值输入
                            if "steps" in input_name.lower():
                                param_name = f"{node_class} - {input_name}"
                                param_path = f"{node_id}.inputs.{input_name}"
                                editable_params[param_name] = param_path
                            elif "cfg" in input_name.lower() or "scale" in input_name.lower():
                                param_name = f"{node_class} - {input_name}"
                                param_path = f"{node_id}.inputs.{input_name}"
                                editable_params[param_name] = param_path
        except Exception as e:
            print(f"警告：提取参数失败: {str(e)}")
        
        return editable_params
    
    def _load_local_config_workflows(self) -> List[WorkflowInfo]:
        """加载本地工作流目录中的工作流"""
        workflows: List[WorkflowInfo] = []
        config_workflows_dir = self.workflows_dir

        if not config_workflows_dir.exists():
            return workflows

        for workflow_dir in config_workflows_dir.iterdir():
            if not workflow_dir.is_dir():
                continue
                
            metadata_file = workflow_dir / "metadata.json"
            workflow_file = workflow_dir / "workflow.json"
            
            if not metadata_file.exists() or not workflow_file.exists():
                continue
                
            try:
                # 读取metadata
                with open(metadata_file, 'r', encoding='utf-8') as f:
                    metadata_data = json.load(f)
                
                # 读取workflow JSON
                with open(workflow_file, 'r', encoding='utf-8') as f:
                    workflow_json = json.load(f)
                
                # 创建WorkflowMetadata对象
                tags = []
                for tag_str in metadata_data.get("tags", ["RDY"]):
                    if tag_str == "DEV":
                        tags.append(WorkflowTag.DEV)
                    else:
                        tags.append(WorkflowTag.RDY)
                
                metadata = WorkflowMetadata(
                    name=metadata_data.get("name", workflow_dir.name),
                    description=metadata_data.get("description", ""),
                    tags=tags,
                    editable_params=metadata_data.get("editable_params", {}),
                    default_input_dir=metadata_data.get("default_input_dir", f"app/data/inputs/{workflow_dir.name}"),
                    default_output_dir=metadata_data.get("default_output_dir", f"app/data/outputs/{workflow_dir.name}")
                )
                
                # 创建WorkflowInfo对象
                workflow_info = WorkflowInfo(
                    key=workflow_dir.name,
                    metadata=metadata,
                    workflow_json=workflow_json,
                    file_path=str(workflow_file),
                    relative_path=workflow_dir.name,
                    parent_dir="本地工作流"
                )
                
                workflows.append(workflow_info)
                
            except Exception as e:
                print(f"警告：加载本地工作流 {workflow_dir.name} 失败: {str(e)}")

                # 尝试从备份恢复
                if "Expecting value" in str(e) or "工作流文件为空" in str(e):
                    backup_files = list(workflow_dir.glob("workflow.json.bak.*"))
                    if backup_files:
                        # 选择最新的备份文件
                        latest_backup = max(backup_files, key=lambda p: p.stat().st_mtime)
                        try:
                            print(f"尝试从备份恢复工作流 {workflow_dir.name}: {latest_backup.name}")
                            with open(latest_backup, 'r', encoding='utf-8') as bf:
                                backup_content = bf.read().strip()
                                if backup_content:
                                    backup_json = json.loads(backup_content)
                                                    # 恢复主文件 - 使用文件锁定和重试机制
                                    import time
                                    import random

                                    restore_attempts = 0
                                    max_restore_attempts = 5

                                    while restore_attempts < max_restore_attempts:
                                        restore_attempts += 1
                                        try:
                                            # 使用唯一的临时文件名避免冲突
                                            temp_restore_path = str(workflow_file) + f".restore_tmp_{restore_attempts}_{random.randint(1000, 9999)}"

                                            with open(temp_restore_path, 'w', encoding='utf-8', newline='\n') as wf:
                                                json.dump(backup_json, wf, ensure_ascii=False, indent=2)
                                                wf.flush()
                                                os.fsync(wf.fileno())

                                            # 验证恢复的文件
                                            with open(temp_restore_path, 'r', encoding='utf-8') as vf:
                                                verify_content = vf.read().strip()
                                                if not verify_content:
                                                    raise Exception("恢复的文件为空")
                                                json.loads(verify_content)  # 验证JSON

                                            # 尝试原子替换，如果失败则重试
                                            try:
                                                os.replace(temp_restore_path, str(workflow_file))
                                                print(f"✅ 成功从备份恢复工作流 {workflow_dir.name} (尝试 {restore_attempts}/{max_restore_attempts})")
                                                break
                                            except OSError as replace_error:
                                                if restore_attempts >= max_restore_attempts:
                                                    raise replace_error
                                                print(f"⚠️ 文件替换失败，重试中... (尝试 {restore_attempts}/{max_restore_attempts})")
                                                time.sleep(0.1 + random.uniform(0, 0.2))  # 随机延迟避免冲突
                                                continue

                                        except Exception as restore_error:
                                            # 清理临时文件
                                            try:
                                                if 'temp_restore_path' in locals() and os.path.exists(temp_restore_path):
                                                    os.remove(temp_restore_path)
                                            except:
                                                pass

                                            if restore_attempts >= max_restore_attempts:
                                                raise restore_error

                                            print(f"⚠️ 恢复尝试 {restore_attempts}/{max_restore_attempts} 失败: {restore_error}")
                                            time.sleep(0.1 + random.uniform(0, 0.2))

                                    if restore_attempts >= max_restore_attempts:
                                        raise Exception(f"恢复失败，已尝试 {max_restore_attempts} 次")

                                    # 重新尝试加载
                                    workflow_json = backup_json
                                    metadata = WorkflowMetadata(
                                        name=metadata_data.get("name", workflow_dir.name),
                                        description=metadata_data.get("description", ""),
                                        tags=tags,
                                        editable_params=metadata_data.get("editable_params", {}),
                                        default_input_dir=metadata_data.get("default_input_dir", f"app/data/inputs/{workflow_dir.name}"),
                                        default_output_dir=metadata_data.get("default_output_dir", f"app/data/outputs/{workflow_dir.name}")
                                    )

                                    workflow_info = WorkflowInfo(
                                        key=workflow_dir.name,
                                        metadata=metadata,
                                        workflow_json=workflow_json,
                                        file_path=str(workflow_file),
                                        relative_path=workflow_dir.name,
                                        parent_dir="本地工作流"
                                    )

                                    workflows.append(workflow_info)
                        except Exception as recovery_error:
                            print(f"从备份恢复失败: {recovery_error}")
        
        return workflows
    
    async def sync_from_comfyui_directory(self, workflow_dir: str) -> Dict[str, Any]:
        """从ComfyUI目录同步工作流"""
        try:
            self.set_comfyui_workflow_dir(workflow_dir)
            workflows = await self.list_workflows_async()
            
            return {
                "success": True,
                "synced_count": len(workflows),
                "message": f"成功从目录 {workflow_dir} 加载 {len(workflows)} 个工作流"
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e)
            }

# 全局工作流服务实例
workflow_service = WorkflowService()
